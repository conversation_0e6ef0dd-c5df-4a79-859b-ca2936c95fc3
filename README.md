<h1 align="center">
  💥 CVE-2025-48703 AutoExploit GUI/CLI 💥
</h1>

<p align="center">
  <img src="https://img.shields.io/badge/status-stable-brightgreen?style=flat-square" />
  <img src="https://img.shields.io/badge/language-python3-blue?style=flat-square" />
  <img src="https://img.shields.io/badge/gui-tkinter-ff00ff?style=flat-square" />
</p>

<pre align="center">

   _______      ________    ___   ___ ___  _____       _  _   ___ ______ ___ ____   
  / ____\ \    / /  ____|  |__ \ / _ \__ \| ____|     | || | / _ \____  / _ \___ \  
 | |     \ \  / /| |__ ______ ) | | | ) | | |__ ______| || || (_) |  / / | | |__) | 
 | |      \ \/ / |  __|______/ /| | | |/ /|___ \______|__   _> _ <  / /| | | |__ <  
 | |____   \  /  | |____    / /_| |_| / /_ ___) |        | || (_) |/ / | |_| |__) | 
  \_____|   \/   |______| _|____|\___/____|____/         |_| \___//_/   \___/____/  

</pre>

---

## 🧠 À propos

**TRHACKNON CVE-2025-48703 AutoExploit GUI** est un outil **GUI interactif** en Python utilisant Tkinter, conçu pour automatiser l'exploitation d'une vulnérabilité **critique** (fictive ou future) ciblant **CWP**.

🔬 Destiné à un usage **éducatif uniquement**, il permet de simuler plusieurs types d'exploits dans une interface intuitive à la sauce **hacker**.

---

## 🎯 Fonctionnalités

- 🇨🇵 Sélection du pays cible via code ISO (`FR`, `RU`, etc.)
- 🔧 Choix entre plusieurs vecteurs d'exploit :
  - ✅ `Chmod .bashrc`
  - ✅ `Reverse Shell Netcat`
  - ✅ `Reverse Shell Meterpreter`
- 🖥️ Terminal de sortie avec couleurs fluo
- 🧪 Progression visuelle de l'exploitation
- 🎨 Design cyberpunk : vert fluo, rose, cyan, terminal sombre

---

## 📸 Aperçu visuel

<h1 align="center" style="color:#39ff14">
  💥 TRHACKNON CVE-2025-48703 AutoExploit 💥
</h1>

<p align="center">
  <img src="https://j.top4top.io/p_34646sno60.jpg" alt="GUI Screenshot" width="600"/>
  <br>
  <i style="color:#00ffff;">GUI version — 💻</i>
</p>

<p align="center">
  <img src="https://k.top4top.io/p_3464bopzv1.jpg" alt="CLI Screenshot" width="600"/>
  <br>
  <i style="color:#00ffff;">CLI version — 💻</i>
</p>


---

## 🚀 Installation & Lancement

### 🐍 Prérequis

- Python 3.x
- OS compatible avec Tkinter (Linux, macOS, Windows)

### 🔧 Installation

```bash
git clone https://github.com/trhacknon/cve-2025-48703-autoexploit.git
cd cve-2025-48703-autoexploit
python3 gui.py
```

### 🔧 USAGE
cli:

```bash
python3 app.py
```
gui:

```bash
python3 gui.py
```

---

⚠️ Disclaimer

> 🚨 AVERTISSEMENT

Cet outil est fourni à des fins éducatives uniquement.
L'auteur trhacknon décline toute responsabilité en cas d'utilisation malveillante. N'utilisez cet outil que dans un environnement autorisé et contrôlé (lab, pentest autorisé, démonstration).


---

<p align="center" style="color:#39ff14;">
  Made with 💚 by trhacknon — style hacker fluo forever ⚡
</p>


---

💬 Contact : trhacknon

🌐 Projet(s) sur GitHub : https://github.com/trh4ckn0n
