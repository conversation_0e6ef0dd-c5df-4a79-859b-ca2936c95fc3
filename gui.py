from tkinter import *
from tkinter import ttk
from tkinter.scrolledtext import ScrolledText

# --- UI Setup ---
root = Tk()
root.title("TRHACKNON CVE-2025-48703 AutoExploit GUI")
root.configure(bg="#0d0d0d")
root.geometry("1000x800")
root.resizable(False, False)

# --- <PERSON>uleurs & Style ---
FLUO_GREEN = "#39ff14"
FLUO_CYAN = "#00ffff"
FLUO_PINK = "#ff00ff"
DARK_BG = "#0d0d0d"
INPUT_BG = "#1a1a1a"
TEXT_WHITE = "#f2f2f2"

style = ttk.Style()
style.theme_use('clam')
style.configure("TLabel", background=DARK_BG, foreground=FLUO_GREEN, font=("Consolas", 11))
style.configure("TButton", background=DARK_BG, foreground=FLUO_CYAN, font=("Consolas", 12, "bold"))
style.map("TButton",
    background=[("active", FLUO_GREEN)],
    foreground=[("active", DARK_BG)]
)

# --- Logo animÃ© ---
logo_frame = Frame(root, bg=DARK_BG)
logo_frame.pack(pady=(10, 0))

logo = """

   _______      ________    ___   ___ ___  _____       _  _   ___ ______ ___ ____   
  / ____\ \    / /  ____|  |__ \ / _ \__ \| ____|     | || | / _ \____  / _ \___ \  
 | |     \ \  / /| |__ ______ ) | | | | ) | |__ ______| || || (_) |  / / | | |__) | 
 | |      \ \/ / |  __|______/ /| | | |/ /|___ \______|__   _> _ <  / /| | | |__ <  
 | |____   \  /  | |____    / /_| |_| / /_ ___) |        | || (_) |/ / | |_| |__) | 
  \_____|   \/   |______| _|____|\___/____|____/         |_| \___//_/   \___/____/  
                 | |     (_) |                                                      
   _____  ___ __ | | ___  _| |_                                                     
  / _ \ \/ / '_ \| |/ _ \| | __|                                                    
 |  __/>  <| |_) | | (_) | | |_                                                     
  \___/_/\_\ .__/|_|\___/|_|\__|           _                                        
 | |       | | |__   __| | |              | |                                       
 | |__  _  |_|    | |_ __| |__   __ _  ___| | ___ __   ___  _ __                    
 | '_ \| | | |    | | '__| '_ \ / _` |/ __| |/ / '_ \ / _ \| '_ \                   
 | |_) | |_| |    | | |  | | | | (_| | (__|   <| | | | (_) | | | |                  
 |_.__/ \__, |    |_|_|  |_| |_|\__,_|\___|_|\_\_| |_|\___/|_| |_|                  
         __/ |                                                                      
        |___/                                                                       
"""

logo_label = Label(logo_frame, text=logo, font=("Consolas", 10), fg=FLUO_GREEN, bg=DARK_BG, justify=LEFT)
logo_label.pack()

# --- Titre ---
Label(root, text="trhacknon CWP AutoExploit", font=("Consolas", 20, "bold"), fg=FLUO_GREEN, bg=DARK_BG).pack(pady=(0, 10))

# --- Frame principal ---
frame = Frame(root, bg=DARK_BG)
frame.pack(pady=10)

country_var = StringVar()
exploit_vars = {"1": IntVar(), "2": IntVar(), "3": IntVar()}

Label(frame, text="Code pays ISO (FR, RU, ...):").grid(row=0, column=0, sticky=W, padx=5, pady=5)

Entry(frame, textvariable=country_var, font=("Consolas", 12),
      bg=INPUT_BG, fg=FLUO_GREEN, insertbackground=FLUO_GREEN,
      width=30, highlightbackground=FLUO_GREEN,
      highlightcolor=FLUO_GREEN, highlightthickness=1,
      relief="flat", borderwidth=5).grid(row=0, column=1, padx=5, pady=5)

Label(frame, text="Choix des exploits:").grid(row=1, column=0, sticky=W, padx=5, pady=5)

Checkbutton(frame, text="Chmod .bashrc", variable=exploit_vars["1"],
            bg=DARK_BG, fg=FLUO_PINK, selectcolor=INPUT_BG,
            font=("Consolas", 10), activeforeground=FLUO_PINK).grid(row=1, column=1, sticky=W)
Checkbutton(frame, text="Reverse Shell Netcat", variable=exploit_vars["2"],
            bg=DARK_BG, fg=FLUO_CYAN, selectcolor=INPUT_BG,
            font=("Consolas", 10), activeforeground=FLUO_CYAN).grid(row=2, column=1, sticky=W)
Checkbutton(frame, text="Reverse Shell Meterpreter", variable=exploit_vars["3"],
            bg=DARK_BG, fg=FLUO_GREEN, selectcolor=INPUT_BG,
            font=("Consolas", 10), activeforeground=FLUO_GREEN).grid(row=3, column=1, sticky=W)

# --- Output ---
output = ScrolledText(root, font=("Consolas", 10), bg="#000000", fg=TEXT_WHITE,
                      insertbackground="#ffffff", borderwidth=2,
                      relief="groove", padx=10, pady=10)
output.pack(fill=BOTH, expand=True, pady=15, padx=20)

# --- Progress Bar ---
progress = ttk.Progressbar(root, mode='determinate', length=500)
progress.pack(pady=(0, 20))

# --- Boutons ---
button_frame = Frame(root, bg=DARK_BG)
button_frame.pack()

def log(msg, color=FLUO_GREEN):
    output.insert(END, f"> {msg}\n", ('colored',))
    output.tag_config('colored', foreground=color)
    output.see(END)

def clear_output():
    output.delete(1.0, END)
    log("Journal nettoyÃ©.", FLUO_PINK)

def start():
    output.insert(END, "\nðŸš€ Lancement de l'exploit...\n", 'cyan')
    output.tag_config('cyan', foreground=FLUO_CYAN)
    progress['value'] = 0
    simulate_progress()

def simulate_progress():
    current = progress['value']
    if current < 100:
        progress['value'] += 5
        root.after(100, simulate_progress)
    else:
        log("âœ… Exploit terminÃ© avec succÃ¨s.", FLUO_GREEN)

start_btn = Button(button_frame, text="ðŸš€ Lancer l'exploit", command=start,
                   font=("Consolas", 12, "bold"), bg=FLUO_PINK, fg=DARK_BG,
                   activebackground=FLUO_GREEN, activeforeground=DARK_BG,
                   relief="flat", padx=20, pady=8)
start_btn.grid(row=0, column=0, padx=20)

reset_btn = Button(button_frame, text="ðŸ§¹ Reset", command=clear_output,
                   font=("Consolas", 12), bg=FLUO_CYAN, fg=DARK_BG,
                   activebackground=FLUO_PINK, activeforeground=DARK_BG,
                   relief="flat", padx=20, pady=8)
reset_btn.grid(row=0, column=1)

# --- Initial log ---
output.insert(END, logo + "\n")
log("Coded by trhacknon | Educational use only", FLUO_GREEN)

root.mainloop()
