#!/usr/bin/env python3
import requests
import os
from dotenv import load_dotenv
import json

# Carregar variáveis de ambiente
load_dotenv()
SECURITYTRAILS_API_KEY = os.getenv("SECURITYTRAILS_API_KEY")

def test_securitytrails_api():
    """Testa se a API key do SecurityTrails está funcionando"""
    
    if not SECURITYTRAILS_API_KEY or SECURITYTRAILS_API_KEY == "SUA_CHAVE_SECURITYTRAILS_AQUI":
        print("❌ SECURITYTRAILS_API_KEY não configurada!")
        print("💡 Para obter uma chave gratuita:")
        print("   1. Acesse: https://securitytrails.com/")
        print("   2. Crie uma conta gratuita")
        print("   3. Vá em 'API' no menu")
        print("   4. Copie sua API key")
        print("   5. Adicione no arquivo .env:")
        print("      SECURITYTRAILS_API_KEY=sua_chave_aqui")
        return False
    
    headers = {
        'APIKEY': SECURITYTRAILS_API_KEY,
        'Content-Type': 'application/json'
    }
    
    base_url = "https://api.securitytrails.com/v1"
    
    try:
        print(f"🔑 Testando API Key: {SECURITYTRAILS_API_KEY[:10]}...")
        
        # Teste 1: Informações da conta
        print("\n📊 Testando informações da conta...")
        url = f"{base_url}/account/usage"
        response = requests.get(url, headers=headers, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ API Key válida!")
            print(f"📈 Uso atual: {json.dumps(data, indent=2)}")
        elif response.status_code == 401:
            print("❌ API Key inválida ou expirada")
            return False
        else:
            print(f"⚠️ Resposta inesperada: {response.status_code} - {response.text}")
        
        # Teste 2: Busca simples por domínio
        print("\n🔍 Testando busca por domínio...")
        url = f"{base_url}/domain/google.com"
        response = requests.get(url, headers=headers, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Busca por domínio funcionando!")
            print(f"📋 Exemplo de dados: {json.dumps(data.get('current_dns', {}), indent=2)[:200]}...")
        else:
            print(f"⚠️ Erro na busca por domínio: {response.status_code}")
        
        # Teste 3: Busca por hosts (funcionalidade principal)
        print("\n🎯 Testando busca por hosts CWP...")
        url = f"{base_url}/hosts/search"
        
        # Query para buscar servidores com portas CWP
        query = {
            "filter": {
                "ports": [2082, 2083, 2086, 2087],
                "keyword": "cwp"
            }
        }
        
        response = requests.post(url, headers=headers, json=query, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            records = data.get('records', [])
            print(f"✅ Busca por hosts funcionando!")
            print(f"📊 Encontrados {len(records)} registros")
            
            if records:
                print("🎯 Exemplos de alvos encontrados:")
                for i, record in enumerate(records[:3]):
                    ip = record.get('ip', 'N/A')
                    hostname = record.get('hostname', 'N/A')
                    ports = record.get('ports', [])
                    print(f"   {i+1}. IP: {ip} | Host: {hostname} | Portas: {[p.get('port') for p in ports]}")
            else:
                print("ℹ️ Nenhum registro encontrado para esta busca específica")
                
        elif response.status_code == 402:
            print("💳 Limite de API atingido - considere upgrade do plano")
        elif response.status_code == 429:
            print("⏰ Rate limit atingido - aguarde antes de tentar novamente")
        else:
            print(f"⚠️ Erro na busca por hosts: {response.status_code} - {response.text}")
        
        # Teste 4: Verificar limites da API
        print("\n📋 Verificando limites da API...")
        url = f"{base_url}/account/usage"
        response = requests.get(url, headers=headers, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            current_monthly = data.get('current_monthly_usage', 0)
            allowed_monthly = data.get('allowed_monthly_usage', 0)
            print(f"📊 Uso mensal: {current_monthly}/{allowed_monthly}")
            
            if current_monthly >= allowed_monthly:
                print("⚠️ Limite mensal atingido!")
            else:
                remaining = allowed_monthly - current_monthly
                print(f"✅ Consultas restantes: {remaining}")
        
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"❌ Erro de conexão: {e}")
        return False
    except Exception as e:
        print(f"❌ Erro inesperado: {e}")
        return False

def show_securitytrails_info():
    """Mostra informações sobre o SecurityTrails"""
    print("\n" + "="*60)
    print("📚 INFORMAÇÕES SOBRE SECURITYTRAILS")
    print("="*60)
    print("🌐 Site: https://securitytrails.com/")
    print("📖 Documentação: https://docs.securitytrails.com/")
    print("💰 Plano gratuito: 50 consultas/mês")
    print("🔍 Funcionalidades:")
    print("   • Busca por hosts e IPs")
    print("   • Histórico de DNS")
    print("   • Informações de subdomínios")
    print("   • Dados de certificados SSL")
    print("   • Tecnologias detectadas")
    print("\n🎯 Vantagens sobre Shodan:")
    print("   • Foco em dados de DNS e domínios")
    print("   • Histórico detalhado de mudanças")
    print("   • Melhor para reconhecimento passivo")
    print("   • API mais estável")
    print("="*60)

if __name__ == "__main__":
    show_securitytrails_info()
    
    print("\n🧪 TESTE DA API SECURITYTRAILS")
    print("="*40)
    
    success = test_securitytrails_api()
    
    if success:
        print("\n✅ SecurityTrails configurado com sucesso!")
        print("🚀 Você pode usar: python app_securitytrails.py")
    else:
        print("\n❌ Configuração do SecurityTrails falhou")
        print("💡 Verifique sua API key e tente novamente")
