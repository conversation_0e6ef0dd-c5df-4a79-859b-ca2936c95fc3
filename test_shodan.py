#!/usr/bin/env python3
import shodan
import os
from dotenv import load_dotenv

# Carregar variáveis de ambiente
load_dotenv()
SHODAN_API_KEY = os.getenv("SHODAN_API_KEY")

def test_shodan_api():
    """Testa se a API key do Shodan está funcionando"""
    try:
        api = shodan.Shodan(SHODAN_API_KEY)
        
        # Teste básico - informações da conta
        print(f"[+] Testando API Key: {SHODAN_API_KEY[:10]}...")
        info = api.info()
        print(f"[+] API Key válida!")
        print(f"[+] Créditos disponíveis: {info.get('query_credits', 'N/A')}")
        print(f"[+] Plano: {info.get('plan', 'N/A')}")
        
        # Teste de busca simples
        print("\n[+] Testando busca simples...")
        results = api.search('apache', limit=1)
        print(f"[+] Busca funcionando! Total de resultados: {results['total']}")
        
        # Teste dos dorks específicos do projeto
        print("\n[+] Testando dorks do projeto...")
        dorks = [
            'Server: cwpsrv',
            'ssl.cert.subject.IL:"centos"'
        ]
        
        for dork in dorks:
            try:
                print(f"[+] Testando dork: {dork}")
                results = api.search(dork, limit=5)
                print(f"    └─ Resultados encontrados: {results['total']}")
                
                if results['matches']:
                    for i, match in enumerate(results['matches'][:3]):
                        ip = match.get('ip_str')
                        port = match.get('port', 'N/A')
                        print(f"    └─ Exemplo {i+1}: {ip}:{port}")
                else:
                    print("    └─ Nenhum resultado encontrado para este dork")
                    
            except shodan.APIError as e:
                print(f"    └─ Erro no dork '{dork}': {e}")
                
    except shodan.APIError as e:
        print(f"[-] Erro na API do Shodan: {e}")
        if "Invalid API key" in str(e):
            print("[-] Sua API key parece estar inválida")
        elif "Insufficient query credits" in str(e):
            print("[-] Você não tem créditos suficientes")
    except Exception as e:
        print(f"[-] Erro inesperado: {e}")

if __name__ == "__main__":
    if not SHODAN_API_KEY:
        print("[-] SHODAN_API_KEY não encontrada no arquivo .env")
        print("[-] Certifique-se de ter um arquivo .env com:")
        print("SHODAN_API_KEY=sua_chave_aqui")
    else:
        test_shodan_api()
